import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Animated,
  ActivityIndicator,
  Modal,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import Header from "../components/Header";
import { useAuth } from "../context/AuthContext";
import { createFadeInAnimation } from "../utils/animations";
import colors from "../styles/colors";
import apiService from "../services/apiService";

export default function DonationListScreen() {
  const [donations, setDonations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [availableYears, setAvailableYears] = useState([]);
  const [stats, setStats] = useState(null);

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [donationToDelete, setDonationToDelete] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();
  const { isAdmin } = useAuth();

  useEffect(() => {
    if (!isAdmin()) {
      navigation.goBack();
      return;
    }
    createFadeInAnimation(fadeAnim, 400).start();
    loadInitialData();
  }, []);

  useEffect(() => {
    if (selectedYear) {
      loadDonations();
      loadYearStats();
    }
  }, [selectedYear]);

  const loadInitialData = async () => {
    try {
      await Promise.all([loadAvailableYears(), loadDonations(), loadYearStats()]);
    } catch (error) {
      console.error("Error loading initial data:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableYears = async () => {
    try {
      const result = await apiService.getAvailableYears();
      if (result.success && result.data) {
        setAvailableYears(result.data.years || []);
      }
    } catch (error) {
      console.error("Error loading available years:", error);
    }
  };

  const loadDonations = async () => {
    try {
      const result = await apiService.getDonationsByYear(selectedYear);
      if (result.success && result.data) {
        setDonations(result.data.donations || []);
      }
    } catch (error) {
      console.error("Error loading donations:", error);
    }
  };

  const loadYearStats = async () => {
    try {
      const result = await apiService.getYearStats(selectedYear);
      if (result.success && result.data) {
        setStats({
          totalRecords: result.data.totalRecords,
          firstDonationDate: result.data.firstDonationDate,
          lastDonationDate: result.data.lastDonationDate,
        });
      }
    } catch (error) {
      console.error("Error loading year stats:", error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDonations();
    await loadYearStats();
    setRefreshing(false);
  };

  const handleDeleteDonation = (donation) => {
    setDonationToDelete(donation);
    setShowDeleteModal(true);
  };

  const confirmDeleteDonation = async () => {
    if (!donationToDelete) return;
    try {
      const result = await apiService.deleteDonation(selectedYear, donationToDelete.id);
      if (result.success) {
        setShowDeleteModal(false);
        setShowSuccessModal(true);
        setDonations((prev) =>
          prev.filter((item) => item.id !== donationToDelete.id)
        );
        setTimeout(() => setShowSuccessModal(false), 2000);
      }
    } catch (error) {
      console.error("Error deleting donation:", error);
    }
  };

  const renderYearSelector = () => (
    <View style={styles.yearSelector}>
      <Text style={styles.yearSelectorTitle}>Select Year:</Text>
      <View style={styles.yearButtons}>
        {availableYears.map((year) => (
          <TouchableOpacity
            key={year}
            style={[
              styles.yearButton,
              selectedYear === year && styles.selectedYearButton,
            ]}
            onPress={() => setSelectedYear(year)}
          >
            <Text
              style={[
                styles.yearButtonText,
                selectedYear === year && styles.selectedYearButtonText,
              ]}
            >
              {year}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderStats = () =>
    stats && (
      <View style={styles.statsContainer}>
        <Text style={styles.statsTitle}>Year {selectedYear} Summary</Text>
        <Text style={styles.statsText}>Total Donations: {stats.totalRecords}</Text>
        {stats.firstDonationDate && (
          <Text style={styles.statsText}>
            First Donation: {new Date(stats.firstDonationDate).toLocaleDateString()}
          </Text>
        )}
        {stats.lastDonationDate && (
          <Text style={styles.statsText}>
            Last Donation: {new Date(stats.lastDonationDate).toLocaleDateString()}
          </Text>
        )}
      </View>
    );

  const renderTable = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.tableContainer}
    >
      <View>
        <View style={[styles.tableRow, styles.tableHeader]}>
          <Text style={[styles.tableCell, styles.headerText, { width: 80 }]}>ID</Text>
          <Text style={[styles.tableCell, styles.headerText, { width: 160 }]}>Donor</Text>
          <Text style={[styles.tableCell, styles.headerText, { width: 120 }]}>Amount</Text>
          <Text style={[styles.tableCell, styles.headerText, { width: 120 }]}>Type</Text>
          <Text style={[styles.tableCell, styles.headerText, { width: 160 }]}>Phone</Text>
          <Text style={[styles.tableCell, styles.headerText, { width: 200 }]}>Address</Text>
          <Text style={[styles.tableCell, styles.headerText, { width: 140 }]}>Date</Text>
          <Text style={[styles.tableCell, styles.headerText, { width: 140 }]}>Created By</Text>
          <Text style={[styles.tableCell, styles.headerText, { width: 100 }]}>Action</Text>
        </View>

        {donations.map((item, index) => (
          <View
            key={item.id || index}
            style={[
              styles.tableRow,
              { backgroundColor: index % 2 === 0 ? "#f9f9f9" : "#fff" },
            ]}
          >
            <Text style={[styles.tableCell, { width: 80 }]}>{item.id}</Text>
            <Text style={[styles.tableCell, { width: 160 }]}>{item.donorName}</Text>
            <Text style={[styles.tableCell, { width: 120 }]}>₹{item.donationAmount}</Text>
            <Text style={[styles.tableCell, { width: 120 }]}>{item.donationType}</Text>
            <Text style={[styles.tableCell, { width: 160 }]}>{item.donorPhone}</Text>
            <Text style={[styles.tableCell, { width: 200 }]}>{item.donorAddress}</Text>
            <Text style={[styles.tableCell, { width: 140 }]}>
              {new Date(item.createdDate).toLocaleDateString()}
            </Text>
            <Text style={[styles.tableCell, { width: 140 }]}>{item.createdBy}</Text>

            <TouchableOpacity
              onPress={() => handleDeleteDonation(item)}
              style={styles.deleteIcon}
            >
              <Ionicons name="trash" size={18} color={colors.error} />
            </TouchableOpacity>
          </View>
        ))}

        {donations.length === 0 && (
          <Text style={styles.emptyText}>No donations found for {selectedYear}</Text>
        )}
      </View>
    </ScrollView>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <Header title="Donation List" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading donations...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header title="Donation List" />
      <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
        {renderYearSelector()}
        {renderStats()}
        <ScrollView
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        >
          {renderTable()}
        </ScrollView>
      </Animated.View>

      {/* 🗑 Delete Modal */}
      <Modal
        visible={showDeleteModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowDeleteModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Ionicons name="warning-outline" size={36} color={colors.error} />
            <Text style={styles.modalTitle}>Delete Donation</Text>
            <Text style={styles.modalMessage}>
              Are you sure you want to delete this donation from{" "}
              <Text style={{ fontWeight: "bold" }}>
                {donationToDelete?.donorName}
              </Text>
              ?
            </Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowDeleteModal(false)}
              >
                <Text style={styles.modalButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.deleteButton]}
                onPress={confirmDeleteDonation}
              >
                <Text style={styles.modalButtonText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* ✅ Success Modal */}
      <Modal visible={showSuccessModal} transparent animationType="fade">
        <View style={styles.successOverlay}>
          <View style={styles.successCard}>
            <Ionicons name="checkmark-circle" size={48} color={colors.success} />
            <Text style={styles.successText}>Donation Deleted!</Text>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: colors.background },
  content: { flex: 1, padding: 16 },
  tableContainer: {
    backgroundColor: colors.cardBg,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 12,
    overflow: "hidden",
  },
  tableHeader: {
    flexDirection: "row",
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 6,
  },
  headerText: {
    color: colors.white,
    fontWeight: "600",
    fontSize: 13,
    textAlign: "center",
  },
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    paddingVertical: 10,
    paddingHorizontal: 6,
  },
  tableCell: {
    textAlign: "center",
    fontSize: 13,
    color: colors.text,
  },
  deleteIcon: {
    justifyContent: "center",
    alignItems: "center",
    width: 100,
  },
  emptyText: {
    fontSize: 16,
    color: colors.textMuted,
    textAlign: "center",
    marginVertical: 20,
  },
  loadingContainer: { flex: 1, justifyContent: "center", alignItems: "center" },
  loadingText: { marginTop: 16, fontSize: 16, color: colors.textLight },

  // Modal styles (same as ManageUser)
  modalContainer: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0,0,0,0.4)",
  },
  modalContent: {
    backgroundColor: colors.cardBg,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.text,
    marginVertical: 10,
  },
  modalMessage: {
    fontSize: 15,
    color: colors.textLight,
    textAlign: "center",
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    marginHorizontal: 6,
    borderRadius: 8,
    alignItems: "center",
  },
  cancelButton: { backgroundColor: colors.border },
  deleteButton: { backgroundColor: colors.error },
  modalButtonText: { color: colors.white, fontWeight: "600" },

  successOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  successCard: {
    backgroundColor: colors.cardBg,
    padding: 30,
    borderRadius: 16,
    alignItems: "center",
  },
  successText: {
    marginTop: 12,
    fontSize: 16,
    fontWeight: "600",
    color: colors.success,
  },
  yearSelector: { marginBottom: 12 },
  yearSelectorTitle: { fontWeight: "600", color: colors.text, marginBottom: 6 },
  yearButtons: { flexDirection: "row", flexWrap: "wrap", gap: 6 },
  yearButton: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  selectedYearButton: { backgroundColor: colors.primary },
  yearButtonText: { color: colors.text },
  selectedYearButtonText: { color: colors.white },
  statsContainer: {
    backgroundColor: colors.cardBg,
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
  },
  statsTitle: { fontWeight: "600", color: colors.primary, marginBottom: 6 },
  statsText: { color: colors.textLight, fontSize: 14 },
});
