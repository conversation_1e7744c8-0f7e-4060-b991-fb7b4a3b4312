export default {
  // Primary orange theme
  primary: "#E16527",
  primaryLight: "#F97316", // Lighter orange for hover states
  primaryDark: "#C2410C",  // Darker orange for pressed states
  primarySoft: "#FED7AA",  // Very light orange for backgrounds

  // Secondary blue (complementary to orange)
  secondary: "#1D4ED8",
  secondaryLight: "#3B82F6",
  secondaryDark: "#1E40AF",
  secondarySoft: "#DBEAFE",

  // Neutral colors
  background: "#FAFAFA",
  cardBg: "#FFFFFF",
  surfaceElevated: "#F8FAFC", // For elevated cards

  // Text colors
  textDark: "#1F2937",
  textMedium: "#374151",
  textLight: "#6B7280",
  textMuted: "#9CA3AF",

  // Status colors
  success: "#059669",
  successLight: "#10B981",
  successSoft: "#D1FAE5",

  warning: "#D97706",
  warningLight: "#F59E0B",
  warningSoft: "#FEF3C7",

  danger: "#DC2626",
  dangerLight: "#EF4444",
  dangerSoft: "#FEE2E2",

  info: "#0EA5E9",
  infoLight: "#38BDF8",
  infoSoft: "#E0F2FE",

  // Border and divider colors
  border: "#E5E7EB",
  borderLight: "#F3F4F6",
  divider: "#D1D5DB",

  // Shadow colors
  shadowLight: "rgba(0, 0, 0, 0.05)",
  shadowMedium: "rgba(0, 0, 0, 0.1)",
  shadowDark: "rgba(0, 0, 0, 0.15)",

  // Overlay colors
  overlay: "rgba(0, 0, 0, 0.5)",
  overlayLight: "rgba(0, 0, 0, 0.3)",

  // Gradient colors
  gradientStart: "#E16527",
  gradientEnd: "#F97316",
};
