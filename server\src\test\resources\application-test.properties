# Test Configuration
spring.application.name=server-test

# Test Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# JPA Configuration for Tests
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false

# JWT Configuration for Tests
app.jwt.secret=testSecretKeyForDigitalPavtiPustakApplicationThatIsLongEnoughForJWTSecurity
app.jwt.expiration=3600000

# Logging for Tests
logging.level.com.app.server=INFO
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
